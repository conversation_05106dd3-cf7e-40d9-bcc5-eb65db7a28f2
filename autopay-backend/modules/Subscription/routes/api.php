<?php

use Illuminate\Support\Facades\Route;
use Modules\Subscription\Http\Controllers\FeatureController;
use Modules\Subscription\Http\Controllers\OrganizationPlanController;
use Modules\Subscription\Http\Controllers\OrganizationSubscriptionController;
use Modules\Subscription\Http\Controllers\TransactionPricingController;

/*
|--------------------------------------------------------------------------
| API Routes for Subscription Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Subscription module.
|
*/

// Public access routes
Route::get('/system-plans', [OrganizationPlanController::class, 'systemPlans']);
Route::get('/features', [FeatureController::class, 'index']);

// Transaction pricing routes
Route::get('/transaction-pricing', [TransactionPricingController::class, 'index']);
Route::post('/transaction-pricing/calculate', [TransactionPricingController::class, 'calculateCost']);

Route::middleware(['auth:sanctum'])->group(function () {

    // Organization-specific routes
    Route::prefix('organizations/{organization}')
        ->middleware('organization.access')
        ->group(function () {

        // Organization Plans Management
        Route::prefix('plans')->group(function () {
            Route::get('/', [OrganizationPlanController::class, 'index']);
            Route::post('/', [OrganizationPlanController::class, 'store']);
            Route::get('/{plan}', [OrganizationPlanController::class, 'show']);
            Route::put('/{plan}', [OrganizationPlanController::class, 'update']);
            Route::patch('/{plan}/toggle-status', [OrganizationPlanController::class, 'toggleStatus']);
            Route::delete('/{plan}', [OrganizationPlanController::class, 'destroy']);
            Route::post('/{plan}/duplicate', [OrganizationPlanController::class, 'duplicate']);
            Route::get('/subscribers', [OrganizationPlanController::class, 'subscribers']);
        });

        // Organization Subscriptions Management
        Route::prefix('subscriptions')->group(function () {
            // Organization-level subscription (to system plans)
            Route::post('/organization', [OrganizationSubscriptionController::class, 'subscribeOrganization']);
            Route::get('/organization', [OrganizationSubscriptionController::class, 'organizationSubscription']);
            Route::delete('/organization', [OrganizationSubscriptionController::class, 'cancelOrganizationSubscription']);
            Route::get('/organization/usage', [OrganizationSubscriptionController::class, 'organizationUsage']);

            // Member subscriptions (to organization plans)
            Route::post('/users', [OrganizationSubscriptionController::class, 'subscribeUser']);
            Route::get('/users/{user}', [OrganizationSubscriptionController::class, 'userSubscription']);
            Route::delete('/users/{user}', [OrganizationSubscriptionController::class, 'cancelUserSubscription']);
            Route::get('/members', [OrganizationSubscriptionController::class, 'membersWithSubscriptions']);
        });
    });
});
