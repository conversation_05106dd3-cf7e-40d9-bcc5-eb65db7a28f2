<?php

namespace Modules\User\Data;

use Modules\User\Models\SocialAccount;
use <PERSON><PERSON>\LaravelData\Data;

class SocialAccountData extends Data
{
    public function __construct(
        public string $id,
        public string $provider,
        public string $name,
        public ?string $avatar,
        public string $account_id,
        public string $created_at,
    ) {}

    public static function fromSocialAccount(SocialAccount $socialAccount): self
    {
        return new self(
            id: $socialAccount->id,
            provider: $socialAccount->provider,
            name: $socialAccount->name,
            avatar: $socialAccount->avatar,
            account_id: $socialAccount->account_id,
            created_at: $socialAccount->created_at->toISOString(),
        );
    }
}
