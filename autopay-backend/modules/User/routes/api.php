<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\User\Http\Controllers\Auth\AuthController;
use Modules\User\Http\Controllers\Auth\LoginController;
use Modules\User\Http\Controllers\Auth\RegisterController;
use Modules\User\Http\Controllers\Auth\SocialController;
use Modules\User\Http\Controllers\Auth\VerificationController;
use Modules\User\Http\Controllers\AuthenticationController;
use Modules\User\Http\Controllers\UserProfileController;

Route::group([
    'middleware' => 'guest',
], static function (): void {
    Route::group([
        'controller' => LoginController::class,
    ], static function (): void {
        Route::post('/login', 'login')->name('login');
    });

    Route::group([
        'controller' => SocialController::class,
    ], static function (): void {
        Route::post('/login/{provider}', 'loginCallback')->name('login.social');
        Route::post('/register/{provider}', 'registerCallback')->name('register.social');

        Route::post('/callback/{action}/{provider}', 'googleOneTapHandle')->name('google.handle');
    });

    Route::group([
        'controller' => RegisterController::class,
    ], static function (): void {
        Route::post('/register', 'register')->name('register');
    });

    // Email Verification Routes
    Route::group([
        'controller' => VerificationController::class,
        'prefix' => 'verification',
        'as' => 'verification.',
        'middleware' => 'throttle:5,1',
    ], static function (): void {
        Route::get('/confirm/{id}/{hash}', 'confirm')->name('confirm')
            ->middleware('signed:relative');
        Route::post('/resend', 'resend')->name('resend');
    });

    Route::group([
        'controller' => AuthController::class,
        'middleware' => 'throttle:5,1',
    ], static function (): void {
        Route::get('/password/reset/{id}/{hash}', 'passwordReset')->name('password.reset')
            ->middleware(['signed:relative']);

        Route::post('/forgot', 'forgot')->name('forgot.check');
    });
});

Route::group([
    'middleware' => ['auth:sanctum'],
], static function (): void {
    Route::group([
        'controller' => AuthController::class,
        'middleware' => 'throttle:5,1',
    ], static function (): void {
        Route::any('/logout', 'logout')->name('logout');
    });

    Route::group([
        'controller' => AuthenticationController::class,
        'prefix' => 'authentication',
        'as' => 'authentication.',
    ], static function (): void {
        Route::get('/2fa', 'generate2fa')->name('generate.2fa');
        Route::post('/2fa', 'check2fa')->name('check.2fa');
    });

    // User profile and organization/team management routes
    Route::group([
        'controller' => UserProfileController::class,
        'prefix' => 'profile',
        'as' => 'profile.',
    ], static function (): void {
        Route::get('/', 'profile')->name('index');
        Route::delete('/', 'deleteProfile')->name('delete');
        Route::patch('/', 'updateProfile')->name('update');

        Route::get('/activity', 'activity')->name('activity');

        Route::patch('/preferences', 'updatePreferences')->name('update-preferences');

        // Notification settings
        Route::get('/notifications', 'getNotificationSettings')->name('notifications.get');
        Route::patch('/notifications', 'updateNotificationSettings')->name('notifications.update');

        // Social accounts management
        Route::get('/social-accounts', 'socialAccounts')->name('social-accounts');
        Route::delete('/social-accounts/{socialAccount}', 'deleteSocialAccount')->name('social-accounts.delete');
        Route::post('/social-accounts/connect/{provider}', 'connectSocialAccount')->name('social-accounts.connect');

        // Personal access tokens management
        Route::get('/tokens', 'tokens')->name('tokens');
        Route::delete('/tokens/{tokenId}', 'deleteToken')->name('tokens.delete');
    });

    Route::group(['middleware' => '2fa'], static function (): void {
        Route::group([
            'controller' => AuthenticationController::class,
            'prefix' => 'authentication',
            'as' => 'authentication.',
        ], static function (): void {
            Route::delete('/2fa', 'disable2fa')->name('disable.2fa');
            Route::patch('/password', 'updatePassword')->name('update.password');
        });
    });
});
