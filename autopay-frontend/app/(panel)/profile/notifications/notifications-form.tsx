'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Switch } from '@/components/ui/switch'
import { toast } from '@/components/ui/use-toast'

const notificationsFormSchema = z.object({
  security_notifications: z.boolean().default(true),
  system_notifications: z.boolean().default(true),
  promotion_notifications: z.boolean().default(false),
})

type NotificationsFormValues = z.infer<typeof notificationsFormSchema>

// This can come from your database or API.
const defaultValues: NotificationsFormValues = {
  security_notifications: true,
  system_notifications: true,
  promotion_notifications: false,
}

export function NotificationsForm() {
  const form = useForm({
    resolver: zodResolver(notificationsFormSchema),
    defaultValues,
  })

  function onSubmit(data: NotificationsFormValues) {
    // TODO: Implement API call to update user data field
    console.log('Notification settings:', data)
    toast({
      title: 'Cập nhật thành công',
      description: 'Cài đặt thông báo đã được lưu.',
    })
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="security_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Thông báo bảo mật</FormLabel>
                  <FormDescription>Nhận thông báo về bảo mật tài khoản và hoạt động đăng nhập.</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled
                    aria-readonly
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="system_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Thông báo hệ thống</FormLabel>
                  <FormDescription>Nhận thông báo về các thay đổi hệ thống, bảo trì và cập nhật.</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="promotion_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Thông báo khuyến mãi</FormLabel>
                  <FormDescription>Nhận thông báo về các chương trình khuyến mãi và ưu đãi đặc biệt.</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <Button
          type="submit"
          size="sm">
          Cập nhật cài đặt
        </Button>
      </form>
    </Form>
  )
}
