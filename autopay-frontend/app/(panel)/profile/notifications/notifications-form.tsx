'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Switch } from '@/components/ui/switch'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'

const notificationsFormSchema = z.object({
  security: z.boolean().default(true),
  system: z.boolean().default(true),
  promotion: z.boolean().default(false),
})

type NotificationsFormValues = z.infer<typeof notificationsFormSchema>

// This can come from your database or API.
const defaultValues: NotificationsFormValues = {
  security: true,
  system: true,
  promotion: false,
}

export function NotificationsForm() {
  // Fetch notification settings
  const { data: settingsData, isLoading } = useQuery<ApiResponse<NotificationsFormValues>>({
    queryKey: ['getNotificationSettings'],
    queryFn: () => queryFetchHelper('/profile/notifications'),
  })

  // Update notification settings mutation
  const updateSettingsMutation = useMutation<ApiResponse<NotificationsFormValues>, Error, NotificationsFormValues>({
    mutationFn: async (settings: NotificationsFormValues) => {
      return queryFetchHelper('/profile/notifications', {
        method: 'PATCH',
        body: JSON.stringify(settings),
      })
    },
    onMutate: () => {
      // Show loading toast
      toast.loading('Đang cập nhật cài đặt...', { id: 'notification-update' })
    },
    onSuccess: (response: any) => {
      toast.success(response.message || 'Cài đặt thông báo đã được cập nhật thành công', { id: 'notification-update' })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật cài đặt', { id: 'notification-update' })
    },
  })

  const form = useForm({
    resolver: zodResolver(notificationsFormSchema),
    defaultValues,
  })

  // Update form when settings are loaded
  useEffect(() => {
    if (settingsData?.data) {
      form.reset(settingsData.data)
    }
  }, [settingsData, form])

  // Handle field change and auto-save
  const handleFieldChange = (field: keyof NotificationsFormValues, value: boolean) => {
    // Update form value
    form.setValue(field, value)

    // Get current form values and update the changed field
    const currentValues = form.getValues()
    const updatedValues: NotificationsFormValues = {
      security: currentValues.security ?? true,
      system: currentValues.system ?? true,
      promotion: currentValues.promotion ?? false,
      [field]: value,
    }

    // Auto-save to backend
    updateSettingsMutation.mutate(updatedValues)
  }

  if (isLoading) {
    return <FormSkeleton />
  }

  return (
    <Form {...form}>
      <div className="space-y-4">
        <FormField
          control={form.control}
          name="security"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>Thông báo bảo mật</FormLabel>
                <FormDescription>Nhận thông báo về bảo mật tài khoản và hoạt động đăng nhập.</FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={(value) => handleFieldChange('security', value)}
                  disabled
                  aria-readonly
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="system"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>Thông báo hệ thống</FormLabel>
                <FormDescription>Nhận thông báo về các thay đổi hệ thống, bảo trì và cập nhật.</FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={(value) => handleFieldChange('system', value)}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="promotion"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>Thông báo khuyến mãi</FormLabel>
                <FormDescription>Nhận thông báo về các chương trình khuyến mãi và ưu đãi đặc biệt.</FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={(value) => handleFieldChange('promotion', value)}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </Form>
  )
}
