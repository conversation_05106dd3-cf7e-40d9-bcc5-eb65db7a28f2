'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Switch } from '@/components/ui/switch'
import { useNotificationSettings, useUpdateNotificationSettings } from '@/lib/hooks/useNotificationSettings'
import { toast } from 'sonner'

const notificationsFormSchema = z.object({
  security_notifications: z.boolean().default(true),
  system_notifications: z.boolean().default(true),
  promotion_notifications: z.boolean().default(false),
})

type NotificationsFormValues = z.infer<typeof notificationsFormSchema>

// This can come from your database or API.
const defaultValues: NotificationsFormValues = {
  security_notifications: true,
  system_notifications: true,
  promotion_notifications: false,
}

export function NotificationsForm() {
  const { settings, isLoading } = useNotificationSettings()
  const updateSettingsMutation = useUpdateNotificationSettings()

  const form = useForm({
    resolver: zodResolver(notificationsFormSchema),
    defaultValues,
  })

  // Update form when settings are loaded
  useEffect(() => {
    if (settings) {
      form.reset(settings)
    }
  }, [settings, form])

  function onSubmit(data: NotificationsFormValues) {
    updateSettingsMutation.mutate(data, {
      onSuccess: (response) => {
        toast.success(response.message || 'Cài đặt thông báo đã được cập nhật thành công')
      },
      onError: (error: any) => {
        toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật cài đặt')
      },
    })
  }

  if (isLoading) {
    return <FormSkeleton />
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="security_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Thông báo bảo mật</FormLabel>
                  <FormDescription>Nhận thông báo về bảo mật tài khoản và hoạt động đăng nhập.</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled
                    aria-readonly
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="system_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Thông báo hệ thống</FormLabel>
                  <FormDescription>Nhận thông báo về các thay đổi hệ thống, bảo trì và cập nhật.</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="promotion_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Thông báo khuyến mãi</FormLabel>
                  <FormDescription>Nhận thông báo về các chương trình khuyến mãi và ưu đãi đặc biệt.</FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <Button
          type="submit"
          size="sm">
          Cập nhật cài đặt
        </Button>
      </form>
    </Form>
  )
}
