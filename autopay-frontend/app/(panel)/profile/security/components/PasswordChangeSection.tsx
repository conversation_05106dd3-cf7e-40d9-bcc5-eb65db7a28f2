'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { Key } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormItemPasswordGenerator from '@/components/custom-ui/form-item-password-generator'
import { Button } from '@/components/ui/button'
import { Form, FormDescription, FormField } from '@/components/ui/form'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

// Password change form schema
const passwordFormSchema = z
  .object({
    password: z.string().min(1, 'Mật khẩu hiện tại là bắt buộc'),
    new_password: z.string().min(8, 'Mật khẩu mới phải có ít nhất 8 ký tự'),
    new_password_confirmation: z.string().min(1, '<PERSON><PERSON><PERSON> nhận mật khẩu là bắt buộc'),
  })
  .refine((data) => data.new_password === data.new_password_confirmation, {
    message: '<PERSON>ật khẩu xác nhận không khớp',
    path: ['new_password_confirmation'],
  })

type PasswordFormValues = z.infer<typeof passwordFormSchema>

// API Response interface
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

export default function PasswordChangeSection() {
  // Password change form
  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      password: '',
      new_password: '',
      new_password_confirmation: '',
    },
  })

  // Password change mutation
  const { isPending: isChangingPassword, mutate: changePassword } = useMutation<ApiResponse, Error, PasswordFormValues>(
    {
      mutationFn: async (data: PasswordFormValues) => {
        return queryFetchHelper('/authentication/password', {
          method: 'PATCH',
          body: JSON.stringify({
            password: data.password,
            new_password: data.new_password,
            new_password_confirmation: data.new_password_confirmation,
          }),
        })
      },
      onSuccess: (data) => {
        toast.success(data.message || 'Mật khẩu đã được cập nhật thành công')
        passwordForm.reset()
      },
      onError: (error: any) => {
        // Handle validation errors from backend
        if (error?.errors) {
          const errors = error.errors

          // Set field-specific errors
          Object.keys(errors).forEach((field) => {
            if (field === 'password' || field === 'new_password' || field === 'new_password_confirmation') {
              passwordForm.setError(field as keyof PasswordFormValues, {
                type: 'server',
                message: Array.isArray(errors[field]) ? errors[field][0] : errors[field],
              })
            }
          })

          // Show general error message if no field-specific errors
          if (
            !Object.keys(errors).some((field) =>
              ['password', 'new_password', 'new_password_confirmation'].includes(field)
            )
          ) {
            toast.error(error.message || 'Cập nhật mật khẩu thất bại')
          }
        } else {
          // Show general error for non-validation errors
          toast.error(error.message || 'Cập nhật mật khẩu thất bại')
        }
      },
    }
  )

  const onPasswordSubmit = (data: PasswordFormValues): void => {
    changePassword(data)
  }

  return (
    <div className="max-w-1/2 space-y-4">
      <div>
        <h2 className="flex items-center gap-2 text-lg font-semibold">
          <Key className="h-5 w-5" />
          Thay đổi mật khẩu
        </h2>
        <p className="text-muted-foreground text-sm">Cập nhật mật khẩu của bạn để bảo mật tài khoản</p>
      </div>
      <div>
        <Form {...passwordForm}>
          <form
            onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
            className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={passwordForm.control}
                name="password"
                render={({ field }) => (
                  <FormItemPasswordGenerator<PasswordFormValues>
                    field={field}
                    label="Mật khẩu hiện tại"
                    showGenerator={false}
                    shouldUpdateValueWhenReceiveEvent={false}
                  />
                )}
              />
              <div></div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={passwordForm.control}
                name="new_password"
                render={({ field }) => (
                  <div>
                    <FormItemPasswordGenerator<PasswordFormValues>
                      field={field}
                      label="Mật khẩu mới"
                      showGenerator={true}
                    />
                    <FormDescription className="mt-2">Mật khẩu phải có ít nhất 8 ký tự</FormDescription>
                  </div>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="new_password_confirmation"
                render={({ field }) => (
                  <div>
                    <FormItemPasswordGenerator<PasswordFormValues>
                      field={field}
                      label="Xác nhận mật khẩu mới"
                      showGenerator={false}
                    />
                    <FormDescription className="mt-2 opacity-0">Placeholder để cân bằng chiều cao</FormDescription>
                  </div>
                )}
              />
            </div>
            <Button
              type="submit"
              disabled={isChangingPassword}>
              {isChangingPassword ? 'Đang cập nhật...' : 'Cập nhật mật khẩu'}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  )
}
