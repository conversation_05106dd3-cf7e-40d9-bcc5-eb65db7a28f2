import { auth } from '@/auth'
import { getClientIp } from '@/lib/utils/clientIp'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { get, has } from 'lodash'
import { Session } from 'next-auth'
import { NextRequest, NextResponse } from 'next/server'
import z from 'zod'

// OAuth provider schema for connection
const oauthConnectSchema = z.object({
  provider: z.enum(['google']),
  oauth: z.object({
    providerAccountId: z.string(),
  }),
  name: z.string(),
  image: z.string().optional(),
})

// API Response interface
interface ApiResponse {
  success: boolean
  code: number
  locale: string
  message: string
  data?: any
}

export async function POST(request: NextRequest) {
  const session: Session | null = await auth()

  if (!session) {
    return NextResponse.json(
      {
        success: false,
        message: 'No session found',
      },
      {
        status: 401,
      }
    )
  }

  const body = await request.json()
  const ip = getClientIp(request) || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'

  // Add user agent and IP to the request body
  const { user } = session

  const data = {
    ip,
    userAgent,
    ...body,
    ...user,
  }

  const validate = oauthConnectSchema.safeParse(data)

  if (!validate.success) {
    return NextResponse.json(
      {
        success: false,
        message: validate.error.errors[0].message,
        errors: validate.error.errors,
      },
      {
        status: 400,
      }
    )
  }

  // Send the request to the API server
  try {
    const response: ApiResponse = await queryFetchHelper(`/profile/social-accounts/connect/${validate.data.provider}`, {
      method: 'POST',
      body: JSON.stringify(validate.data),
    })

    return NextResponse.json(response)
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'An error occurred',
      },
      {
        status: 500,
      }
    )
  }
}
