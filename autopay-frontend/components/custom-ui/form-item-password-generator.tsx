'use client'

import { But<PERSON> } from '@/components/ui/button'
import { FormControl, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import emitter from '@/lib/utils/eventBus'
import NiceModal from '@ebay/nice-modal-react'
import { startCase } from 'lodash'
import { useTranslations } from 'next-intl'
import React, { useEffect, useState } from 'react'
import { ControllerRenderProps, FieldValues, Path } from 'react-hook-form'
import { CiLock } from 'react-icons/ci'
import { FaRegEye, FaRegEyeSlash } from 'react-icons/fa'

interface FormItemPasswordGeneratorProps<T extends FieldValues> {
  field: ControllerRenderProps<T, Path<T>>
  label?: string
  showGenerator?: boolean
  shouldUpdateValueWhenReceiveEvent?: boolean
  rightLabel?: React.ReactNode
  tabIndex?: number
}

const FormItemPasswordGenerator = <T extends FieldValues>({
  field,
  label,
  showGenerator = true,
  shouldUpdateValueWhenReceiveEvent = true,
  rightLabel,
  tabIndex = 0,
}: FormItemPasswordGeneratorProps<T>) => {
  const t = useTranslations('General')

  const [reveal, setReveal] = useState(false)
  const EyeComponents = {
    hidden: FaRegEyeSlash,
    visible: FaRegEye,
  }
  const EyeReveal = EyeComponents[reveal ? 'visible' : 'hidden']

  const showPasswordGenerator = async () => {
    await NiceModal.show('password-generator')
  }

  // Handle password generator event with proper cleanup
  useEffect(() => {
    const handlePasswordGenerated = (password: string) => {
      if (!shouldUpdateValueWhenReceiveEvent) {
        return
      }
      field.onChange(password)
    }

    emitter.on('usePasswordGenerator', handlePasswordGenerated)

    // Cleanup event listener on "unmount"
    return () => {
      emitter.off('usePasswordGenerator', handlePasswordGenerated)
    }
  }, [field, shouldUpdateValueWhenReceiveEvent])

  return (
    <FormItem>
      <FormLabel className="flex h-8 items-center justify-between font-semibold">
        {label || startCase(field.name)}
        {showGenerator && (
          <Button
            type="button"
            size="sm"
            variant="secondary"
            onClick={() => showPasswordGenerator()}
            tabIndex={tabIndex + 1}>
            <CiLock className="size-4" />
            {t('passwordGenerator')}
          </Button>
        )}
        {rightLabel}
      </FormLabel>
      <FormControl>
        <div className="relative flex items-center justify-between">
          <Input
            {...field}
            type={!reveal ? 'password' : 'text'}
            placeholder={startCase(label || field.name)}
          />
          <Button
            type="button"
            className="text-muted-foreground absolute right-0"
            variant="link"
            onClick={() => setReveal(!reveal)}
            data-tooltip-html={t(reveal ? 'hidePassword' : 'showPassword')}
            tabIndex={tabIndex + 1}>
            <EyeReveal className="size-4" />
          </Button>
        </div>
      </FormControl>
      <FormMessage />
    </FormItem>
  )
}

FormItemPasswordGenerator.displayName = 'FormItemPasswordGenerator'

export default FormItemPasswordGenerator
