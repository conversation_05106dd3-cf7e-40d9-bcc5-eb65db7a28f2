import { queryF<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils/fetchHelper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

export interface NotificationSettings {
  security_notifications: boolean
  system_notifications: boolean
  promotion_notifications: boolean
}

export const useNotificationSettings = () => {
  const { data, isLoading, error } = useQuery<ApiResponse<NotificationSettings>>({
    queryKey: ['getNotificationSettings'],
    queryFn: () => queryFetchHelper('/profile/notifications'),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    settings: data?.data || null,
    isLoading,
    error,
  }
}

export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient()

  return useMutation<ApiResponse<NotificationSettings>, Error, NotificationSettings>({
    mutationFn: async (settings: NotificationSettings) => {
      return queryFetchHelper('/profile/notifications', {
        method: 'PATCH',
        body: JSON.stringify(settings),
      })
    },
    onSuccess: () => {
      // Invalidate and refetch notification settings
      queryClient.invalidateQueries({ queryKey: ['getNotificationSettings'] })
    },
  })
}
