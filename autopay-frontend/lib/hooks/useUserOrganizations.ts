import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'

export const useUserOrganizations = () => {
  const { data, isLoading, error } = useQuery<ApiResponse<Organization[]>>({
    queryKey: ['getUserOrganizations'],
    queryFn: () => queryFetchHelper('/organizations'),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    organizations: data?.data || [],
    isLoading,
    error,
  }
}
