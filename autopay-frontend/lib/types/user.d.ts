interface User {
  id: string
  name: string
  first_name?: string
  last_name?: string
  email: string
  phone?: string
  avatar?: string
  two_factor?: {
    enabled?: boolean
    secret_code?: string
  }
  current_organization?: {
    id: string
    name?: string
    alias: string
    is_owner?: boolean
  }
  current_team?: {
    id: string
    name?: string
    alias: string
    is_owner?: boolean
  }

  [key: string]: any
}

interface UserRole {
  id: string
  name: string
  permissions: Permission[]
  organization_id?: string
  team_id?: string
  is_default?: boolean
}
